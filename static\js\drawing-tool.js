// Rectangle Drawing Tool for TradingView Lightweight Charts
// Optimized and refactored version

// Utility functions
const utils = {
    positionsBox(p1, p2, pixelRatio) {
        const start = Math.min(p1, p2) * pixelRatio;
        const end = Math.max(p1, p2) * pixelRatio;
        return {
            position: Math.round(start),
            length: Math.round(end - start)
        };
    },

    ensureDefined(value) {
        if (value === null || value === undefined) {
            throw new Error('Value is null or undefined');
        }
        return value;
    },

    // Optimized color conversion with caching
    _colorCache: new Map(),
    hexToRgba(hex, opacity = 0.3) {
        const key = `${hex}_${opacity}`;
        if (this._colorCache.has(key)) {
            return this._colorCache.get(key);
        }

        const r = parseInt(hex.substr(1, 3), 16);
        const g = parseInt(hex.substr(3, 5), 16);
        const b = parseInt(hex.substr(5, 7), 16);
        const result = `rgba(${r}, ${g}, ${b}, ${opacity})`;

        this._colorCache.set(key, result);
        return result;
    },

    extractColorFromRgba(rgba) {
        const match = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
        if (match) {
            const r = parseInt(match[1]).toString(16).padStart(2, '0');
            const g = parseInt(match[2]).toString(16).padStart(2, '0');
            const b = parseInt(match[3]).toString(16).padStart(2, '0');
            return `#${r}${g}${b}`;
        }
        return '#6495ED';
    },

    getOpacityFromRgba(rgba) {
        const match = rgba.match(/rgba?\([^,]+,[^,]+,[^,]+,\s*([^)]+)\)/);
        return match ? Math.round(parseFloat(match[1]) * 100) : 30;
    }
};

// Simplified plugin base class
class PluginBase {
    constructor() {
        this._chart = null;
        this._series = null;
        this._requestUpdate = null;
    }

    attached({ chart, series, requestUpdate }) {
        this._chart = chart;
        this._series = series;
        this._requestUpdate = requestUpdate;
        this.requestUpdate();
    }

    detached() {
        this._chart = this._series = this._requestUpdate = null;
    }

    get chart() { return utils.ensureDefined(this._chart); }
    get series() { return utils.ensureDefined(this._series); }

    requestUpdate() {
        this._requestUpdate?.();
    }

    updateAllViews() { this.requestUpdate(); }

    // Default empty implementations
    priceAxisViews() { return []; }
    timeAxisViews() { return []; }
    paneViews() { return []; }
    priceAxisPaneViews() { return []; }
    timeAxisPaneViews() { return []; }
}

// Optimized rectangle renderer
class RectanglePaneRenderer {
    constructor(p1, p2, fillColor, isSelected = false, options = {}) {
        this._p1 = p1;
        this._p2 = p2;
        this._fillColor = fillColor;
        this._isSelected = isSelected;
        this._options = options;
    }

    draw(target) {
        target.useBitmapCoordinateSpace(scope => {
            // Early return for invalid coordinates
            if (!this._isValidCoordinates()) return;

            const ctx = scope.context;
            const hPos = utils.positionsBox(this._p1.x, this._p2.x, scope.horizontalPixelRatio);
            const vPos = utils.positionsBox(this._p1.y, this._p2.y, scope.verticalPixelRatio);

            // Batch drawing operations for better performance
            this._drawRectangle(ctx, hPos, vPos);

            if (this._options.showCenterLine) {
                this._drawCenterLine(ctx, hPos, vPos, scope.horizontalPixelRatio);
            }

            if (this._isSelected) {
                this._drawSelectionHighlight(ctx, hPos, vPos, scope.horizontalPixelRatio);
            }
        });
    }

    _isValidCoordinates() {
        return this._p1.x !== null && this._p1.y !== null &&
               this._p2.x !== null && this._p2.y !== null;
    }

    _drawRectangle(ctx, hPos, vPos) {
        ctx.fillStyle = this._fillColor;
        ctx.fillRect(hPos.position, vPos.position, hPos.length, vPos.length);
    }

    _drawCenterLine(ctx, hPos, vPos, pixelRatio) {
        const centerY = vPos.position + vPos.length * 0.5;

        ctx.strokeStyle = this._options.centerLineColor || 'rgba(255, 255, 255, 0.6)';
        ctx.lineWidth = (this._options.centerLineWidth || 1) * pixelRatio;

        const dashPattern = this._options.centerLineDash || [4, 4];
        ctx.setLineDash(dashPattern.map(dash => dash * pixelRatio));

        ctx.beginPath();
        ctx.moveTo(hPos.position, centerY);
        ctx.lineTo(hPos.position + hPos.length, centerY);
        ctx.stroke();
        ctx.setLineDash([]);
    }

    _drawSelectionHighlight(ctx, hPos, vPos, pixelRatio) {
        const { position: x, length: width } = hPos;
        const { position: y, length: height } = vPos;

        // Selection border
        ctx.strokeStyle = '#2962FF';
        ctx.lineWidth = 2 * pixelRatio;
        ctx.setLineDash([5 * pixelRatio, 5 * pixelRatio]);
        ctx.strokeRect(x, y, width, height);
        ctx.setLineDash([]);

        // Resize handles - optimized calculation
        this._drawResizeHandles(ctx, x, y, width, height, pixelRatio);
    }

    _drawResizeHandles(ctx, x, y, width, height, pixelRatio) {
        const handleSize = 8 * pixelRatio;
        const halfHandle = handleSize * 0.5;
        const midX = x + width * 0.5;
        const midY = y + height * 0.5;

        // Pre-calculate handle positions
        const handles = [
            [x - halfHandle, y - halfHandle],                    // top-left
            [midX - halfHandle, y - halfHandle],                 // top-center
            [x + width - halfHandle, y - halfHandle],            // top-right
            [x - halfHandle, midY - halfHandle],                 // middle-left
            [x + width - halfHandle, midY - halfHandle],         // middle-right
            [x - halfHandle, y + height - halfHandle],           // bottom-left
            [midX - halfHandle, y + height - halfHandle],        // bottom-center
            [x + width - halfHandle, y + height - halfHandle]    // bottom-right
        ];

        // Batch handle drawing
        ctx.fillStyle = '#2962FF';
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = pixelRatio;

        handles.forEach(([hx, hy]) => {
            ctx.fillRect(hx, hy, handleSize, handleSize);
            ctx.strokeRect(hx, hy, handleSize, handleSize);
        });
    }
}

// Horizontal line renderer
class HorizontalLinePaneRenderer {
    constructor(price, lineColor, isSelected = false, options = {}) {
        this._price = price;
        this._lineColor = lineColor;
        this._isSelected = isSelected;
        this._options = options;
    }

    draw(target) {
        target.useBitmapCoordinateSpace(scope => {
            if (this._price === null) return;

            const ctx = scope.context;
            const y = this._price * scope.verticalPixelRatio;
            const width = scope.bitmapSize.width;

            // Draw the horizontal line
            ctx.strokeStyle = this._lineColor;
            ctx.lineWidth = (this._options.lineWidth || 1) * scope.verticalPixelRatio;

            // Apply line style
            if (this._options.lineStyle === 1) { // dashed
                ctx.setLineDash((this._options.lineDash || [5, 5]).map(d => d * scope.verticalPixelRatio));
            } else if (this._options.lineStyle === 2) { // dotted
                ctx.setLineDash([1, 3].map(d => d * scope.verticalPixelRatio));
            } else { // solid
                ctx.setLineDash([]);
            }

            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
            ctx.setLineDash([]);

            // Draw selection highlight if selected
            if (this._isSelected) {
                this._drawSelectionHighlight(ctx, y, width, scope.verticalPixelRatio);
            }
        });
    }

    _drawSelectionHighlight(ctx, y, width, pixelRatio) {
        // Draw selection indicator - small squares at both ends
        const handleSize = 8 * pixelRatio;
        const halfHandle = handleSize * 0.5;

        ctx.fillStyle = '#2962FF';
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = pixelRatio;

        // Left handle
        ctx.fillRect(-halfHandle, y - halfHandle, handleSize, handleSize);
        ctx.strokeRect(-halfHandle, y - halfHandle, handleSize, handleSize);

        // Right handle
        ctx.fillRect(width - halfHandle, y - halfHandle, handleSize, handleSize);
        ctx.strokeRect(width - halfHandle, y - halfHandle, handleSize, handleSize);

        // Center handle
        const centerX = width * 0.5;
        ctx.fillRect(centerX - halfHandle, y - halfHandle, handleSize, handleSize);
        ctx.strokeRect(centerX - halfHandle, y - halfHandle, handleSize, handleSize);
    }
}

// Horizontal line pane view
class HorizontalLinePaneView {
    constructor(source) {
        this._source = source;
        this._priceCoordinate = null;
    }

    update() {
        try {
            const { series } = this._source;
            if (!series) {
                this._priceCoordinate = null;
                return;
            }

            this._priceCoordinate = series.priceToCoordinate(this._source._price);
        } catch (error) {
            this._priceCoordinate = null;
        }
    }

    renderer() {
        return new HorizontalLinePaneRenderer(
            this._priceCoordinate,
            this._source._options.lineColor,
            this._source._selected,
            this._source._options
        );
    }
}

// Optimized rectangle pane view
class RectanglePaneView {
    constructor(source) {
        this._source = source;
        this._coordinates = { p1: { x: null, y: null }, p2: { x: null, y: null } };
    }

    update() {
        try {
            const { series, chart } = this._source;
            if (!series || !chart) {
                this._resetCoordinates();
                return;
            }

            const timeScale = chart.timeScale();
            this._coordinates = {
                p1: {
                    x: timeScale.timeToCoordinate(this._source._p1.time),
                    y: series.priceToCoordinate(this._source._p1.price)
                },
                p2: {
                    x: timeScale.timeToCoordinate(this._source._p2.time),
                    y: series.priceToCoordinate(this._source._p2.price)
                }
            };
        } catch (error) {
            this._resetCoordinates();
        }
    }

    _resetCoordinates() {
        this._coordinates = { p1: { x: null, y: null }, p2: { x: null, y: null } };
    }

    renderer() {
        return new RectanglePaneRenderer(
            this._coordinates.p1,
            this._coordinates.p2,
            this._source._options.fillColor,
            this._source._selected,
            this._source._options
        );
    }
}

// Optimized default options with better performance
const DEFAULT_OPTIONS = Object.freeze({
    fillColor: 'rgba(200, 50, 100, 0.75)',
    previewFillColor: 'rgba(200, 50, 100, 0.25)',
    labelColor: 'rgba(200, 50, 100, 1)',
    labelTextColor: 'white',
    showLabels: true,
    showCenterLine: true,
    centerLineColor: 'rgba(255, 255, 255, 0.6)',
    centerLineWidth: 0.5, // Reduced from 1 to 0.5
    centerLineDash: [3, 3], // Reduced from [4, 4] to [3, 3]
    priceLabelFormatter: (price) => price.toFixed(2),
    timeLabelFormatter: (time) => {
        if (typeof time === 'string') return time;
        if (time?.year) {
            return new Date(time.year, time.month - 1, time.day).toLocaleDateString();
        }
        return new Date(time * 1000).toLocaleDateString();
    },
});

// Default options for horizontal line tool
const HORIZONTAL_LINE_DEFAULT_OPTIONS = Object.freeze({
    lineColor: 'rgba(41, 98, 255, 0.8)',
    previewLineColor: 'rgba(41, 98, 255, 0.4)',
    lineWidth: 1,
    lineStyle: 0, // 0 = solid, 1 = dashed, 2 = dotted
    lineDash: [5, 5],
    showLabels: true,
    labelColor: 'rgba(41, 98, 255, 1)',
    labelTextColor: 'white',
    priceLabelFormatter: (price) => price.toFixed(2),
});

// Main HorizontalLine class
class HorizontalLine extends PluginBase {
    constructor(price, options = {}) {
        super();
        this._price = price;
        this._options = { ...HORIZONTAL_LINE_DEFAULT_OPTIONS, ...options };
        this._paneViews = [new HorizontalLinePaneView(this)];
        this._selected = false;
        this._id = Math.random().toString(36).substring(2, 11);
    }

    updateAllViews() {
        try {
            this._paneViews[0]?.update();
        } catch (error) {
            // Silently handle detached lines
        }
    }

    paneViews() { return this._paneViews; }

    applyOptions(options) {
        Object.assign(this._options, options);
        this.requestUpdate();
    }

    setSelected(selected) {
        if (this._selected !== selected) {
            this._selected = selected;
            this.updateAllViews();
            this.requestUpdate();
        }
    }

    isSelected() { return this._selected; }
    getId() { return this._id; }

    // Check if a point is near the horizontal line
    containsPoint(time, price, tolerance = 0.01) {
        const priceRange = Math.abs(price - this._price);
        const maxPrice = Math.max(price, this._price);
        const relativeTolerance = maxPrice * tolerance;
        return priceRange <= relativeTolerance;
    }

    updatePrice(price) {
        this._price = price;
        this.updateAllViews();
        this.requestUpdate();
    }

    getPrice() {
        return this._price;
    }
}

// Preview HorizontalLine class for live drawing
class PreviewHorizontalLine extends HorizontalLine {
    constructor(price, options = {}) {
        super(price, options);
        this._options.lineColor = this._options.previewLineColor;
    }

    updatePrice(price) {
        this._price = price;
        this._paneViews[0]?.update();
        this.requestUpdate();
    }
}

// Optimized Rectangle class with consolidated methods
class Rectangle extends PluginBase {
    constructor(p1, p2, options = {}) {
        super();
        this._p1 = p1;
        this._p2 = p2;
        this._options = { ...DEFAULT_OPTIONS, ...options };
        this._paneViews = [new RectanglePaneView(this)];
        this._selected = false;
        this._id = Math.random().toString(36).substring(2, 11);
    }

    updateAllViews() {
        try {
            this._paneViews[0]?.update(); // Only one pane view, optimize access
        } catch (error) {
            // Silently handle detached rectangles
        }
    }

    paneViews() { return this._paneViews; }

    applyOptions(options) {
        Object.assign(this._options, options); // More efficient than spread
        this.requestUpdate();
    }

    setSelected(selected) {
        if (this._selected !== selected) { // Avoid unnecessary updates
            this._selected = selected;
            this.updateAllViews();
            this.requestUpdate();
        }
    }

    isSelected() { return this._selected; }
    getId() { return this._id; }

    // Optimized bounds calculation with caching
    _getBounds() {
        if (!this._cachedBounds || this._boundsInvalid) {
            this._cachedBounds = {
                minTime: Math.min(this._p1.time, this._p2.time),
                maxTime: Math.max(this._p1.time, this._p2.time),
                minPrice: Math.min(this._p1.price, this._p2.price),
                maxPrice: Math.max(this._p1.price, this._p2.price)
            };
            this._boundsInvalid = false;
        }
        return this._cachedBounds;
    }

    containsPoint(time, price) {
        const bounds = this._getBounds();
        return time >= bounds.minTime && time <= bounds.maxTime &&
               price >= bounds.minPrice && price <= bounds.maxPrice;
    }

    updatePoints(p1, p2) {
        this._p1 = p1;
        this._p2 = p2;
        this._boundsInvalid = true; // Invalidate cached bounds
        this.updateAllViews();
        this.requestUpdate();
    }

    // Optimized live resize for smooth performance
    liveResizeUpdate(p1, p2) {
        this._p1 = p1;
        this._p2 = p2;
        this._boundsInvalid = true;
        this._paneViews[0]?.update();
        this.requestUpdate();
    }

    // Optimized resize handle calculation
    getResizeHandles() {
        const bounds = this._getBounds();
        const midTime = (bounds.minTime + bounds.maxTime) * 0.5;
        const midPrice = (bounds.minPrice + bounds.maxPrice) * 0.5;

        return {
            'top-left': { time: bounds.minTime, price: bounds.maxPrice },
            'top-center': { time: midTime, price: bounds.maxPrice },
            'top-right': { time: bounds.maxTime, price: bounds.maxPrice },
            'middle-left': { time: bounds.minTime, price: midPrice },
            'middle-right': { time: bounds.maxTime, price: midPrice },
            'bottom-left': { time: bounds.minTime, price: bounds.minPrice },
            'bottom-center': { time: midTime, price: bounds.minPrice },
            'bottom-right': { time: bounds.maxTime, price: bounds.minPrice }
        };
    }

    getResizeHandle(time, price, tolerance = 0.02) {
        if (!this._selected) return null;

        const bounds = this._getBounds();
        const timeRange = bounds.maxTime - bounds.minTime;
        const priceRange = bounds.maxPrice - bounds.minPrice;
        const timeTolerance = timeRange * tolerance;
        const priceTolerance = priceRange * tolerance;

        const handles = this.getResizeHandles();

        // Use for...of for better performance than Object.entries
        for (const [handleName, handlePos] of Object.entries(handles)) {
            if (Math.abs(time - handlePos.time) <= timeTolerance &&
                Math.abs(price - handlePos.price) <= priceTolerance) {
                return handleName;
            }
        }
        return null;
    }

    // Consolidated resize logic to eliminate duplication
    _getResizeCoordinates(handleName, newTime, newPrice) {
        const bounds = this._getBounds();

        // Resize mapping - more efficient than duplicate switch statements
        const resizeMap = {
            'top-left': [newTime, newPrice, bounds.maxTime, bounds.minPrice],
            'top-center': [bounds.minTime, newPrice, bounds.maxTime, bounds.minPrice],
            'top-right': [bounds.minTime, newPrice, newTime, bounds.minPrice],
            'middle-left': [newTime, bounds.maxPrice, bounds.maxTime, bounds.minPrice],
            'middle-right': [bounds.minTime, bounds.maxPrice, newTime, bounds.minPrice],
            'bottom-left': [newTime, bounds.maxPrice, bounds.maxTime, newPrice],
            'bottom-center': [bounds.minTime, bounds.maxPrice, bounds.maxTime, newPrice],
            'bottom-right': [bounds.minTime, bounds.maxPrice, newTime, newPrice]
        };

        return resizeMap[handleName];
    }

    liveResizeToHandle(handleName, newTime, newPrice) {
        const coords = this._getResizeCoordinates(handleName, newTime, newPrice);
        if (coords) {
            this.liveResizeUpdate(
                { time: coords[0], price: coords[1] },
                { time: coords[2], price: coords[3] }
            );
        }
    }

    resizeToHandle(handleName, newTime, newPrice) {
        const coords = this._getResizeCoordinates(handleName, newTime, newPrice);
        if (coords) {
            this.updatePoints(
                { time: coords[0], price: coords[1] },
                { time: coords[2], price: coords[3] }
            );
        }
    }
}

// Optimized preview rectangle for live drawing
class PreviewRectangle extends Rectangle {
    constructor(p1, p2, options = {}) {
        super(p1, p2, options);
        this._options.fillColor = this._options.previewFillColor;
    }

    updateEndPoint(p) {
        this._p2 = p;
        this._boundsInvalid = true; // Invalidate cached bounds
        this._paneViews[0]?.update();
        this.requestUpdate();
    }
}

// Optimized Rectangle Drawing Tool class with Horizontal Line support
export class RectangleDrawingTool {
    constructor(chart, series, options = {}) {
        // Core properties
        this._chart = chart;
        this._series = series;
        this._defaultOptions = options;
        this._horizontalLineOptions = options.horizontalLine || {};

        // State management
        this._state = {
            rectangles: [],
            horizontalLines: [],
            previewRectangle: null,
            previewHorizontalLine: null,
            points: [],
            drawing: false,
            drawingMode: 'rectangle', // 'rectangle' or 'horizontalLine'
            selectedRectangle: null,
            selectedHorizontalLine: null,
            resizing: false,
            resizeHandle: null,
            crosshairPosition: null,
            useCrosshairForDrawing: false
        };

        // UI elements
        this._toolbox = null;
        this._extendedRectangles = new Set();

        // Bind event handlers once for better performance
        this._clickHandler = (param) => this._onClick(param);
        this._moveHandler = (param) => this._onMouseMove(param);
        this._visibleRangeHandler = () => this._updateExtendedRectangles();

        // Setup event listeners
        this._setupEventListeners();
    }

    _setupEventListeners() {
        this._chart.subscribeClick(this._clickHandler);
        this._chart.subscribeCrosshairMove(this._moveHandler);
        this._chart.timeScale().subscribeVisibleTimeRangeChange(this._visibleRangeHandler);
        this._setupMouseEventHandler();
        this._setupKeyboardHandlers();
    }

    remove() {
        this.stopDrawing();
        this._cleanupEventListeners();
        this._cleanupRectangles();
        this._hideToolbox();
        this._chart = this._series = null;
    }

    _cleanupEventListeners() {
        if (!this._chart) return;

        // Chart event listeners
        this._chart.unsubscribeClick(this._clickHandler);
        this._chart.unsubscribeCrosshairMove(this._moveHandler);
        this._chart.timeScale().unsubscribeVisibleTimeRangeChange(this._visibleRangeHandler);

        // Document event listeners
        if (this._keydownHandler) {
            document.removeEventListener('keydown', this._keydownHandler);
        }
        if (this._keyupHandler) {
            document.removeEventListener('keyup', this._keyupHandler);
        }

        // Chart element event listeners
        const chartElement = this._chart.chartElement();
        if (chartElement) {
            const handlers = [
                'mouseClickHandler', 'mouseDownHandler',
                'mouseMoveHandler', 'mouseUpHandler'
            ];
            handlers.forEach(handler => {
                if (this[`_${handler}`]) {
                    chartElement.removeEventListener(
                        handler.replace('Handler', '').replace('mouse', '').toLowerCase(),
                        this[`_${handler}`],
                        true
                    );
                }
            });
        }
    }

    _cleanupRectangles() {
        this._state.rectangles.forEach(rectangle => this._removeRectangle(rectangle));
        this._state.rectangles = [];
        this._removePreviewRectangle();

        this._state.horizontalLines.forEach(line => this._removeHorizontalLine(line));
        this._state.horizontalLines = [];
        this._removePreviewHorizontalLine();
    }

    startDrawing() {
        this._state.drawing = true;
        this._state.points = [];

        // Add preview for horizontal line mode
        if (this._state.drawingMode === 'horizontalLine' && this._state.crosshairPosition) {
            this._addPreviewHorizontalLine(this._state.crosshairPosition.price);
        }
    }

    stopDrawing() {
        this._state.drawing = false;
        this._state.points = [];
        this._removePreviewRectangle();
        this._removePreviewHorizontalLine();
    }

    isDrawing() { return this._state.drawing; }

    // Drawing mode management
    setDrawingMode(mode) {
        if (mode !== 'rectangle' && mode !== 'horizontalLine') {
            throw new Error('Invalid drawing mode. Use "rectangle" or "horizontalLine"');
        }
        this._state.drawingMode = mode;
        this.stopDrawing(); // Stop any current drawing
    }

    getDrawingMode() {
        return this._state.drawingMode;
    }

    _onClick(param) {
        if (!this._isValidClickParam(param)) return;

        const price = this._series.coordinateToPrice(param.point.y);
        if (price === null) return;

        const isCtrlClick = param.originalEvent?.ctrlKey || param.ctrlKey;

        if (isCtrlClick) {
            this._handleShapeSelection(param.time, price);
        } else if ((this._state.selectedRectangle || this._state.selectedHorizontalLine) && !this._state.drawing) {
            this._handleSelectedShapeClick(param.time, price);
        } else if (this._state.drawing) {
            this._handleDrawingClick(param, price);
        } else {
            // Always try to deselect when clicking elsewhere
            this._deselectAllShapes();
        }
    }

    _isValidClickParam(param) {
        return param?.point && param?.time && this._series;
    }

    _handleResizeStart(time, price) {
        const handle = this._state.selectedRectangle.getResizeHandle(time, price);
        if (handle) {
            this._startResize(handle);
        }
    }

    _handleSelectedShapeClick(time, price) {
        if (this._state.selectedRectangle) {
            // Check if clicking on resize handle first
            const handle = this._state.selectedRectangle.getResizeHandle(time, price);
            if (handle) {
                this._startResize(handle);
                return;
            }
            // Check if clicking inside the selected rectangle
            if (!this._state.selectedRectangle.containsPoint(time, price)) {
                this._deselectAllShapes();
            }
        } else if (this._state.selectedHorizontalLine) {
            // Check if clicking on the horizontal line
            if (!this._state.selectedHorizontalLine.containsPoint(time, price)) {
                this._deselectAllShapes();
            }
        }
    }

    _handleDrawingClick(param, price) {
        const drawingPosition = this._state.useCrosshairForDrawing ?
            this._getCrosshairOHLCPosition(param) :
            { time: param.time, price };

        if (this._state.drawingMode === 'horizontalLine') {
            this._addHorizontalLine(drawingPosition.price);
        } else {
            this._addPoint(drawingPosition);
        }
    }

    _handleShapeSelection(time, price) {
        // First check rectangles
        const clickedRectangle = this._state.rectangles.find(rect => rect.containsPoint(time, price));
        if (clickedRectangle) {
            this._selectRectangle(clickedRectangle);
            return;
        }

        // Then check horizontal lines
        const clickedHorizontalLine = this._state.horizontalLines.find(line => line.containsPoint(time, price));
        if (clickedHorizontalLine) {
            this._selectHorizontalLine(clickedHorizontalLine);
            return;
        }

        // If nothing found, deselect all
        this._deselectAllShapes();
    }

    _onMouseMove(param) {
        if (!this._isValidClickParam(param)) return;

        const price = this._series.coordinateToPrice(param.point.y);
        if (price === null) return;

        // Update crosshair state
        this._state.crosshairPosition = { time: param.time, price };
        this._updateCrosshairDrawingMode();

        const drawingPosition = this._state.useCrosshairForDrawing ?
            this._getCrosshairOHLCPosition(param) :
            { time: param.time, price };

        // Handle different interaction modes
        if (this._state.drawing) {
            if (this._state.drawingMode === 'horizontalLine' && this._state.previewHorizontalLine) {
                this._state.previewHorizontalLine.updatePrice(drawingPosition.price);
            } else if (this._state.drawingMode === 'rectangle' && this._state.previewRectangle) {
                this._state.previewRectangle.updateEndPoint(drawingPosition);
            }
        } else if (this._state.resizing && this._state.selectedRectangle && this._state.resizeHandle) {
            this._state.selectedRectangle.liveResizeToHandle(
                this._state.resizeHandle,
                drawingPosition.time,
                drawingPosition.price
            );
        } else if (this._state.selectedRectangle && !this._state.drawing && !this._state.resizing) {
            const handle = this._state.selectedRectangle.getResizeHandle(param.time, price);
            this._updateCursor(handle);
        }
    }

    _addPoint(p) {
        this._state.points.push(p);

        if (this._state.points.length >= 2) {
            this._completeRectangle();
        } else if (this._state.points.length === 1) {
            this._addPreviewRectangle(this._state.points[0]);
        }
    }

    _addHorizontalLine(price) {
        this._addNewHorizontalLine(price);
        this.stopDrawing();
        this._updateNavbarButton(false);
    }

    _completeRectangle() {
        this._addNewRectangle(this._state.points[0], this._state.points[1]);
        this.stopDrawing();
        this._removePreviewRectangle();
        this._updateNavbarButton(false);
    }

    _updateNavbarButton(isDrawing) {
        const drawingToolBtn = document.getElementById('drawing-tool-btn');
        if (!drawingToolBtn) return;

        drawingToolBtn.classList.toggle('active', isDrawing);
        drawingToolBtn.style.background = isDrawing ? '#2962FF' : '';
    }

    // Horizontal line management
    _addNewHorizontalLine(price) {
        const horizontalLine = new HorizontalLine(price, { ...this._horizontalLineOptions });
        this._state.horizontalLines.push(horizontalLine);
        utils.ensureDefined(this._series).attachPrimitive(horizontalLine);
    }

    _removeHorizontalLine(horizontalLine) {
        utils.ensureDefined(this._series).detachPrimitive(horizontalLine);
    }

    _selectHorizontalLine(horizontalLine) {
        this._deselectAllShapes();
        this._state.selectedHorizontalLine = horizontalLine;
        horizontalLine.setSelected(true);
        this._showHorizontalLineToolbox(horizontalLine);
    }

    _deselectAllShapes() {
        if (this._state.selectedRectangle) {
            this._state.selectedRectangle.setSelected(false);
            this._state.selectedRectangle = null;
        }
        if (this._state.selectedHorizontalLine) {
            this._state.selectedHorizontalLine.setSelected(false);
            this._state.selectedHorizontalLine = null;
        }
        this._hideToolbox();
    }

    // Legacy methods for backward compatibility
    _handleRectangleSelection(time, price) {
        this._handleShapeSelection(time, price);
    }

    _selectRectangle(rectangle) {
        this._deselectAllShapes();
        this._state.selectedRectangle = rectangle;
        rectangle.setSelected(true);
        this._showToolbox(rectangle);
    }

    _deselectRectangle() {
        this._deselectAllShapes();
    }

    _addNewRectangle(p1, p2) {
        const rectangle = new Rectangle(p1, p2, { ...this._defaultOptions });
        this._state.rectangles.push(rectangle);
        utils.ensureDefined(this._series).attachPrimitive(rectangle);
    }

    _removeRectangle(rectangle) {
        utils.ensureDefined(this._series).detachPrimitive(rectangle);
    }

    _addPreviewRectangle(p) {
        this._state.previewRectangle = new PreviewRectangle(p, p, { ...this._defaultOptions });
        utils.ensureDefined(this._series).attachPrimitive(this._state.previewRectangle);
    }

    _removePreviewRectangle() {
        if (this._state.previewRectangle) {
            utils.ensureDefined(this._series).detachPrimitive(this._state.previewRectangle);
            this._state.previewRectangle = null;
        }
    }

    _addPreviewHorizontalLine(price) {
        this._state.previewHorizontalLine = new PreviewHorizontalLine(price, { ...this._horizontalLineOptions });
        utils.ensureDefined(this._series).attachPrimitive(this._state.previewHorizontalLine);
    }

    _removePreviewHorizontalLine() {
        if (this._state.previewHorizontalLine) {
            utils.ensureDefined(this._series).detachPrimitive(this._state.previewHorizontalLine);
            this._state.previewHorizontalLine = null;
        }
    }

    _showToolbox(rectangle) {
        this._hideToolbox();
        this._toolbox = this._createToolbox(rectangle);
        this._appendToolboxToDOM();
    }

    _createToolbox(rectangle) {
        const toolbox = this._createElement('div', {
            style: `
                position: absolute; top: 12px; left: 50%; transform: translateX(-50%);
                background: rgba(35, 38, 47, 0.95); border: 1px solid #363C4E;
                border-radius: 6px; padding: 6px; z-index: 1000;
                display: flex; align-items: center; gap: 4px;
                backdrop-filter: blur(4px); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            `
        });

        // Create toolbox buttons efficiently
        const buttons = this._createToolboxButtons(rectangle);
        buttons.forEach((button, index) => {
            if (index > 0) {
                toolbox.appendChild(this._createSeparator());
            }
            toolbox.appendChild(button);
        });

        return toolbox;
    }

    _createToolboxButtons(rectangle) {
        return [
            this._createColorButton(rectangle),
            this._createExtendButton(rectangle),
            this._createCenterLineButton(rectangle),
            this._createDeleteButton(rectangle)
        ];
    }

    _createColorButton(rectangle) {
        const colorBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s; position: relative;
            `,
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="color: #B2B5BE;">
                    <path d="M12 2C17.5 2 22 6.5 22 12C22 13.8 21.2 15.5 20 16.7L18.3 15C19.3 14.2 20 13.2 20 12C20 7.6 16.4 4 12 4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20H16V22H12C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2Z" fill="currentColor"/>
                    <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
                    <circle cx="15.5" cy="8.5" r="1.5" fill="currentColor"/>
                    <circle cx="8.5" cy="15.5" r="1.5" fill="currentColor"/>
                    <circle cx="15.5" cy="15.5" r="1.5" fill="currentColor"/>
                    <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
                </svg>
            `
        });

        this._addButtonHoverEffect(colorBtn);
        colorBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._showColorPicker(rectangle, colorBtn);
        });

        return colorBtn;
    }

    _createExtendButton(rectangle) {
        const extendBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s;
            `,
            title: 'Extend to Last Visible Bar',
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect x="2" y="4" width="8" height="8" stroke="#B2B5BE" stroke-width="1" fill="none"/>
                    <path d="M10 6L14 8L10 10V9H8V7H10V6Z" fill="#2962FF"/>
                    <line x1="14" y1="2" x2="14" y2="14" stroke="#2962FF" stroke-width="2"/>
                </svg>
            `
        });

        this._addButtonHoverEffect(extendBtn, 'rgba(41, 98, 255, 0.2)');
        extendBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._extendRectangleRight(rectangle);
        });

        return extendBtn;
    }

    _createCenterLineButton(rectangle) {
        const centerLineBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s;
                background-color: ${rectangle._options.showCenterLine ? 'rgba(41, 98, 255, 0.2)' : 'transparent'};
            `,
            title: 'Toggle Center Line',
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect x="2" y="4" width="12" height="8" stroke="#B2B5BE" stroke-width="1" fill="none"/>
                    <line x1="2" y1="8" x2="14" y2="8" stroke="#2962FF" stroke-width="1" stroke-dasharray="2,2"/>
                </svg>
            `
        });

        this._addCenterLineButtonHover(centerLineBtn, rectangle);
        centerLineBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._toggleCenterLine(rectangle, centerLineBtn);
        });

        return centerLineBtn;
    }

    _createDeleteButton(rectangle) {
        const deleteBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s;
            `,
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="color: #F23645;">
                    <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"/>
                </svg>
            `
        });

        this._addButtonHoverEffect(deleteBtn, 'rgba(242, 54, 69, 0.2)');
        deleteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._deleteRectangle(rectangle);
        });

        return deleteBtn;
    }

    // Helper methods for toolbox creation
    _createElement(tag, options = {}) {
        const element = document.createElement(tag);
        Object.entries(options).forEach(([key, value]) => {
            if (key === 'style') {
                element.style.cssText = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else {
                element[key] = value;
            }
        });
        return element;
    }

    _createSeparator() {
        return this._createElement('div', {
            style: `
                width: 1px; height: 20px; background: #363C4E; margin: 0 2px;
            `
        });
    }

    _addButtonHoverEffect(button, hoverColor = 'rgba(255, 255, 255, 0.1)') {
        button.addEventListener('mouseenter', () => {
            button.style.backgroundColor = hoverColor;
        });
        button.addEventListener('mouseleave', () => {
            button.style.backgroundColor = 'transparent';
        });
    }

    _addCenterLineButtonHover(button, rectangle) {
        button.addEventListener('mouseenter', () => {
            if (!rectangle._options.showCenterLine) {
                button.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            }
        });
        button.addEventListener('mouseleave', () => {
            button.style.backgroundColor = rectangle._options.showCenterLine ?
                'rgba(41, 98, 255, 0.2)' : 'transparent';
        });
    }

    _appendToolboxToDOM() {
        const chartElement = this._chart.chartElement();
        const container = chartElement?.parentElement || document.body;
        container.appendChild(this._toolbox);
    }

    _showHorizontalLineToolbox(horizontalLine) {
        this._hideToolbox();
        this._toolbox = this._createHorizontalLineToolbox(horizontalLine);
        this._appendToolboxToDOM();
    }

    _createHorizontalLineToolbox(horizontalLine) {
        const toolbox = this._createElement('div', {
            style: `
                position: absolute; top: 12px; left: 50%; transform: translateX(-50%);
                background: rgba(35, 38, 47, 0.95); border: 1px solid #363C4E;
                border-radius: 6px; padding: 6px; z-index: 1000;
                display: flex; align-items: center; gap: 4px;
                backdrop-filter: blur(4px); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            `
        });

        // Create horizontal line toolbox buttons
        const buttons = [
            this._createHorizontalLineColorButton(horizontalLine),
            this._createHorizontalLineStyleButton(horizontalLine),
            this._createHorizontalLineDeleteButton(horizontalLine)
        ];

        buttons.forEach((button, index) => {
            if (index > 0) {
                toolbox.appendChild(this._createSeparator());
            }
            toolbox.appendChild(button);
        });

        return toolbox;
    }

    _createHorizontalLineColorButton(horizontalLine) {
        const colorBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s; position: relative;
            `,
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="color: #B2B5BE;">
                    <path d="M12 2C17.5 2 22 6.5 22 12C22 13.8 21.2 15.5 20 16.7L18.3 15C19.3 14.2 20 13.2 20 12C20 7.6 16.4 4 12 4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20H16V22H12C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2Z" fill="currentColor"/>
                    <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
                    <circle cx="15.5" cy="8.5" r="1.5" fill="currentColor"/>
                    <circle cx="8.5" cy="15.5" r="1.5" fill="currentColor"/>
                    <circle cx="15.5" cy="15.5" r="1.5" fill="currentColor"/>
                    <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
                </svg>
            `
        });

        this._addButtonHoverEffect(colorBtn);
        colorBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._showHorizontalLineColorPicker(horizontalLine, colorBtn);
        });

        return colorBtn;
    }

    _createHorizontalLineStyleButton(horizontalLine) {
        const styleBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s;
            `,
            title: 'Toggle Line Style',
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <line x1="2" y1="8" x2="14" y2="8" stroke="#B2B5BE" stroke-width="2" stroke-dasharray="${horizontalLine._options.lineStyle === 1 ? '3,3' : horizontalLine._options.lineStyle === 2 ? '1,2' : 'none'}"/>
                </svg>
            `
        });

        this._addButtonHoverEffect(styleBtn);
        styleBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._toggleHorizontalLineStyle(horizontalLine, styleBtn);
        });

        return styleBtn;
    }

    _createHorizontalLineDeleteButton(horizontalLine) {
        const deleteBtn = this._createElement('div', {
            style: `
                width: 28px; height: 28px; border-radius: 4px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
                transition: background-color 0.2s;
            `,
            innerHTML: `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="color: #F23645;">
                    <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"/>
                </svg>
            `
        });

        this._addButtonHoverEffect(deleteBtn, 'rgba(242, 54, 69, 0.2)');
        deleteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._deleteHorizontalLine(horizontalLine);
        });

        return deleteBtn;
    }

    _showHorizontalLineColorPicker(horizontalLine, anchorElement) {
        // Reuse the existing color picker but apply to horizontal line
        this._currentHorizontalLine = horizontalLine;
        this._showColorPicker(horizontalLine, anchorElement);
    }

    _toggleHorizontalLineStyle(horizontalLine, buttonElement) {
        const currentStyle = horizontalLine._options.lineStyle || 0;
        const newStyle = (currentStyle + 1) % 3; // Cycle through 0, 1, 2

        horizontalLine.applyOptions({ lineStyle: newStyle });

        // Update button icon
        const svg = buttonElement.querySelector('svg line');
        if (svg) {
            svg.setAttribute('stroke-dasharray',
                newStyle === 1 ? '3,3' : newStyle === 2 ? '1,2' : 'none'
            );
        }
    }

    _deleteHorizontalLine(horizontalLine) {
        const index = this._state.horizontalLines.indexOf(horizontalLine);
        if (index === -1) return;

        // Clean up state
        if (this._state.selectedHorizontalLine === horizontalLine) {
            this._state.selectedHorizontalLine = null;
            this._hideToolbox();
        }

        this._state.horizontalLines.splice(index, 1);
        this._removeHorizontalLine(horizontalLine);
    }

    _hideToolbox() {
        if (this._toolbox) {
            this._toolbox.remove();
            this._toolbox = null;
        }
        this._hideColorPicker();
    }

    _showColorPicker(rectangle, anchorElement) {
        this._hideColorPicker();
        this._colorPickerPopup = this._createColorPickerPopup(rectangle, anchorElement);
        this._positionColorPicker(anchorElement);
        document.body.appendChild(this._colorPickerPopup);
        this._setupColorPickerCloseHandler();
    }

    _createColorPickerPopup(rectangle) {
        const popup = this._createElement('div', {
            style: `
                position: absolute; background: #2A2D3A; border: 1px solid #363C4E;
                border-radius: 8px; padding: 12px; box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
                z-index: 10000; min-width: 200px;
            `
        });

        popup.appendChild(this._createColorGrid(rectangle));
        popup.appendChild(this._createOpacitySection(rectangle));
        return popup;
    }

    _createColorGrid(rectangle) {
        const colorGrid = this._createElement('div', {
            style: `
                display: grid; grid-template-columns: repeat(10, 1fr);
                gap: 4px; margin-bottom: 12px;
            `
        });

        // Optimized color palette - reduced and organized
        const colors = [
            '#ffffff', '#e6e6e6', '#cccccc', '#b3b3b3', '#999999', '#808080', '#666666', '#4d4d4d', '#333333', '#000000',
            '#f23645', '#ff8000', '#ffff00', '#80ff00', '#00ff80', '#00ffff', '#0080ff', '#8000ff', '#ff00ff', '#ff0080',
            '#fccbcd', '#ffe6cc', '#ffffe6', '#e6ffe6', '#e6ffff', '#e6f3ff', '#e6e6ff', '#f3e6ff', '#ffe6ff', '#ffe6f3',
            '#faa1a4', '#ffcc99', '#ffffcc', '#ccffcc', '#ccffff', '#cce6ff', '#ccccff', '#e6ccff', '#ffccff', '#ffcce6',
            '#f77c80', '#ff9966', '#ffff99', '#99ff99', '#99ffff', '#99ccff', '#9999ff', '#cc99ff', '#ff99ff', '#ff99cc',
            '#f7525f', '#ff6633', '#ffff66', '#66ff66', '#66ffff', '#6699ff', '#6666ff', '#9966ff', '#ff66ff', '#ff6699',
            '#b22833', '#ff3300', '#ffff33', '#33ff33', '#33ffff', '#3366ff', '#3333ff', '#6633ff', '#ff33ff', '#ff3366',
            '#801922', '#cc3300', '#cccc00', '#00cc00', '#00cccc', '#0066cc', '#0000cc', '#3300cc', '#cc00cc', '#cc0066'
        ];

        // Create color swatches efficiently
        colors.forEach(color => {
            const colorSwatch = this._createElement('div', {
                style: `
                    width: 16px; height: 16px; background-color: ${color};
                    border: 1px solid #555; border-radius: 2px; cursor: pointer;
                    transition: transform 0.1s;
                `
            });

            colorSwatch.addEventListener('mouseenter', () => colorSwatch.style.transform = 'scale(1.1)');
            colorSwatch.addEventListener('mouseleave', () => colorSwatch.style.transform = 'scale(1)');
            colorSwatch.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this._applyColorWithOpacity(rectangle, color);
            });

            colorGrid.appendChild(colorSwatch);
        });

        return colorGrid;
    }

    _createOpacitySection(rectangle) {
        const opacitySection = this._createElement('div', {
            style: 'border-top: 1px solid #363C4E; padding-top: 12px;'
        });

        const opacityLabel = this._createElement('div', {
            style: 'color: #B2B5BE; font-size: 12px; margin-bottom: 8px;',
            textContent: 'Opacity'
        });

        const opacityContainer = this._createElement('div', {
            style: 'display: flex; align-items: center; gap: 8px;'
        });

        const opacitySlider = this._createElement('input', {
            type: 'range',
            min: '0',
            max: '100',
            value: utils.getOpacityFromRgba(rectangle._options.fillColor),
            style: `
                flex: 1; height: 4px; background: linear-gradient(to right, #363C4E, #2962FF);
                border-radius: 2px; outline: none; -webkit-appearance: none;
            `
        });

        const opacityValue = this._createElement('div', {
            style: 'color: #B2B5BE; font-size: 12px; min-width: 35px;',
            textContent: opacitySlider.value + '%'
        });

        // Add slider event listener
        opacitySlider.addEventListener('input', (e) => {
            opacityValue.textContent = e.target.value + '%';
            this._currentOpacity = parseInt(e.target.value) / 100;
            this._applyCurrentColorWithOpacity(rectangle);
        });

        opacityContainer.appendChild(opacitySlider);
        opacityContainer.appendChild(opacityValue);
        opacitySection.appendChild(opacityLabel);
        opacitySection.appendChild(opacityContainer);

        return opacitySection;
    }

    _positionColorPicker(anchorElement) {
        const rect = anchorElement.getBoundingClientRect();
        this._colorPickerPopup.style.left = rect.left + 'px';
        this._colorPickerPopup.style.top = (rect.bottom + 8) + 'px';
    }

    _setupColorPickerCloseHandler() {
        // Store current color for opacity changes
        let colorSource = '#6495ED';
        let opacitySource = 'rgba(100, 149, 237, 0.3)';

        if (this._currentHorizontalLine) {
            colorSource = this._currentHorizontalLine._options.lineColor || '#2962FF';
            opacitySource = this._currentHorizontalLine._options.lineColor || 'rgba(41, 98, 255, 0.8)';
        } else if (this._currentRectangle) {
            colorSource = this._currentRectangle._options.fillColor || '#6495ED';
            opacitySource = this._currentRectangle._options.fillColor || 'rgba(100, 149, 237, 0.3)';
        }

        this._currentColor = utils.extractColorFromRgba(colorSource);
        this._currentOpacity = utils.getOpacityFromRgba(opacitySource) / 100;

        setTimeout(() => {
            document.addEventListener('click', this._closeColorPickerOnOutsideClick.bind(this), { once: true });
        }, 100);
    }

    _hideColorPicker() {
        if (this._colorPickerPopup) {
            this._colorPickerPopup.remove();
            this._colorPickerPopup = null;
        }
    }

    _closeColorPickerOnOutsideClick(e) {
        if (this._colorPickerPopup && !this._colorPickerPopup.contains(e.target)) {
            this._hideColorPicker();
        }
    }

    _applyColorWithOpacity(shape, color) {
        this._currentColor = color;
        this._currentShape = shape;
        this._applyCurrentColorWithOpacity(shape);
    }

    _applyCurrentColorWithOpacity(shape) {
        const opacity = this._currentOpacity || 0.3;
        const rgba = utils.hexToRgba(this._currentColor, opacity);

        if (shape instanceof HorizontalLine || this._currentHorizontalLine) {
            // Apply to horizontal line
            const line = shape || this._currentHorizontalLine;
            line.applyOptions({ lineColor: rgba });
        } else {
            // Apply to rectangle
            shape.applyOptions({ fillColor: rgba });
        }
    }

    _closeColorPickerOnOutsideClick(e) {
        if (this._colorPickerPopup && !this._colorPickerPopup.contains(e.target)) {
            this._hideColorPicker();
        }
    }

    _toggleCenterLine(rectangle, buttonElement) {
        const newShowCenterLine = !rectangle._options.showCenterLine;
        rectangle.applyOptions({ showCenterLine: newShowCenterLine });

        // Update button appearance efficiently
        buttonElement.style.backgroundColor = newShowCenterLine ? 'rgba(41, 98, 255, 0.2)' : 'transparent';
        buttonElement.title = newShowCenterLine ? 'Hide Center Line' : 'Show Center Line';
    }

    _extendRectangleRight(rectangle) {
        try {
            const timeScale = this._chart.timeScale();
            const visibleRange = timeScale.getVisibleRange();
            if (!visibleRange) return;

            const lastVisibleTime = visibleRange.to;
            const currentMaxTime = Math.max(rectangle._p1.time, rectangle._p2.time);

            // Early return if already extended
            if (currentMaxTime >= lastVisibleTime) return;

            // Mark as extended and update
            this._extendedRectangles.add(rectangle);

            // Determine which point to update
            const isP1RightEdge = rectangle._p1.time === currentMaxTime;
            const newP1 = isP1RightEdge ?
                { time: lastVisibleTime, price: rectangle._p1.price } : rectangle._p1;
            const newP2 = isP1RightEdge ? rectangle._p2 :
                { time: lastVisibleTime, price: rectangle._p2.price };

            rectangle.updatePoints(newP1, newP2);
        } catch (error) {
            // Silently handle errors
        }
    }

    _updateExtendedRectangles() {
        if (this._extendedRectangles.size === 0) return;

        try {
            const timeScale = this._chart.timeScale();
            const visibleRange = timeScale.getVisibleRange();
            if (!visibleRange) return;

            const newLastVisibleTime = visibleRange.to;

            // Use for...of for better performance than forEach
            for (const rectangle of this._extendedRectangles) {
                // Clean up non-existent rectangles
                if (!this._state.rectangles.includes(rectangle)) {
                    this._extendedRectangles.delete(rectangle);
                    continue;
                }

                const currentMaxTime = Math.max(rectangle._p1.time, rectangle._p2.time);

                // Only update if extending further
                if (newLastVisibleTime > currentMaxTime) {
                    const isP1RightEdge = rectangle._p1.time === currentMaxTime;
                    const newP1 = isP1RightEdge ?
                        { time: newLastVisibleTime, price: rectangle._p1.price } : rectangle._p1;
                    const newP2 = isP1RightEdge ? rectangle._p2 :
                        { time: newLastVisibleTime, price: rectangle._p2.price };

                    rectangle.updatePoints(newP1, newP2);
                }
            }
        } catch (error) {
            // Silently handle errors
        }
    }

    _deleteRectangle(rectangle) {
        const index = this._state.rectangles.indexOf(rectangle);
        if (index === -1) return;

        // Clean up state
        if (this._state.selectedRectangle === rectangle) {
            this._state.selectedRectangle = null;
            this._hideToolbox();
        }

        this._extendedRectangles.delete(rectangle);
        this._state.rectangles.splice(index, 1);
        this._removeRectangle(rectangle);
    }

    _setupMouseEventHandler() {
        // Get the chart's DOM element
        const chartElement = this._chart.chartElement();
        if (!chartElement) return;

        this._mouseClickHandler = (e) => {
            if (!e.ctrlKey) return;

            e.preventDefault();
            e.stopPropagation();

            const rect = chartElement.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const timeScale = this._chart.timeScale();
            const time = timeScale.coordinateToTime(x);
            const price = this._series.coordinateToPrice(y);

            if (time !== null && price !== null) {
                this._handleRectangleSelection(time, price);
            }
        };

        this._mouseDownHandler = (e) => {
            if (!this._state.selectedRectangle || this._state.drawing) return;

            const rect = chartElement.getBoundingClientRect();
            const timeScale = this._chart.timeScale();
            const time = timeScale.coordinateToTime(e.clientX - rect.left);
            const price = this._series.coordinateToPrice(e.clientY - rect.top);

            if (time !== null && price !== null) {
                const handle = this._state.selectedRectangle.getResizeHandle(time, price);
                if (handle) {
                    e.preventDefault();
                    e.stopPropagation();
                    this._startResize(handle);
                    this._disableChartInteractions();
                }
            }
        };

        this._mouseMoveHandler = (e) => {
            if (this._state.selectedRectangle && !this._state.drawing && !this._state.resizing) {
                const rect = chartElement.getBoundingClientRect();
                const timeScale = this._chart.timeScale();
                const time = timeScale.coordinateToTime(e.clientX - rect.left);
                const price = this._series.coordinateToPrice(e.clientY - rect.top);

                if (time !== null && price !== null) {
                    const handle = this._state.selectedRectangle.getResizeHandle(time, price);
                    this._updateCursor(handle);
                }
            }
        };

        // Add the event listeners with capture to intercept before chart
        chartElement.addEventListener('click', this._mouseClickHandler, true);
        chartElement.addEventListener('mousedown', this._mouseDownHandler, true);
        chartElement.addEventListener('mousemove', this._mouseMoveHandler, true);

        // Add mouse up handler to stop resizing
        this._mouseUpHandler = () => {
            if (this._state.resizing) {
                this._stopResize();
                this._enableChartInteractions();
            }
        };
        chartElement.addEventListener('mouseup', this._mouseUpHandler, true);
    }

    _setupKeyboardHandlers() {
        this._keydownHandler = (e) => {
            if (e.key === 'Escape') {
                if (this._state.drawing) {
                    this._cancelDrawing();
                } else if (this._state.selectedRectangle) {
                    this._deselectRectangle();
                }
            }

            if (e.key === 'Control') {
                this._updateCrosshairDrawingMode();
            }
        };

        this._keyupHandler = (e) => {
            if (e.key === 'Control') {
                this._updateCrosshairDrawingMode();
            }
        };

        document.addEventListener('keydown', this._keydownHandler);
        document.addEventListener('keyup', this._keyupHandler);
    }

    _cancelDrawing() {
        this._state.drawing = false;
        this._state.points = [];
        this._removePreviewRectangle();
        this._removePreviewHorizontalLine();
        this._updateNavbarButton(false);
    }

    // Public API methods for horizontal lines
    getHorizontalLines() {
        return [...this._state.horizontalLines];
    }

    removeAllHorizontalLines() {
        this._state.horizontalLines.forEach(line => this._removeHorizontalLine(line));
        this._state.horizontalLines = [];
        if (this._state.selectedHorizontalLine) {
            this._state.selectedHorizontalLine = null;
            this._hideToolbox();
        }
    }

    _startResize(handle) {
        this._state.resizing = true;
        this._state.resizeHandle = handle;
    }

    _stopResize() {
        this._state.resizing = false;
        this._state.resizeHandle = null;
        this._updateCursor(null);
    }

    _updateCursor(handle) {
        const chartElement = this._chart.chartElement();
        if (!chartElement) return;

        const cursorMap = {
            'top-left': 'nw-resize',
            'top-center': 'n-resize',
            'top-right': 'ne-resize',
            'middle-left': 'w-resize',
            'middle-right': 'e-resize',
            'bottom-left': 'sw-resize',
            'bottom-center': 's-resize',
            'bottom-right': 'se-resize'
        };

        const newCursor = cursorMap[handle] || 'default';
        if (chartElement.style.cursor !== newCursor) {
            chartElement.style.cursor = newCursor;
        }
    }

    _disableChartInteractions() {
        const chartElement = this._chart.chartElement();
        if (chartElement && chartElement.style.pointerEvents !== 'none') {
            this._originalPointerEvents = chartElement.style.pointerEvents;
            chartElement.style.pointerEvents = 'none';
            setTimeout(() => {
                if (chartElement && this._state.resizing) {
                    chartElement.style.pointerEvents = 'auto';
                }
            }, 10);
        }
    }

    _enableChartInteractions() {
        const chartElement = this._chart.chartElement();
        if (chartElement) {
            chartElement.style.pointerEvents = this._originalPointerEvents || 'auto';
        }
    }

    // Optimized crosshair-based drawing methods
    _updateCrosshairDrawingMode() {
        const chartOptions = this._chart.options();
        const crosshairMode = chartOptions?.crosshair?.mode;
        this._state.useCrosshairForDrawing = crosshairMode === 3;
    }

    _getCrosshairOHLCPosition(param) {
        if (!param?.time || !param?.seriesData) {
            return { time: param?.time || 0, price: 0 };
        }

        const seriesData = param.seriesData.get(this._series);
        if (!seriesData || typeof seriesData !== 'object') {
            return {
                time: param.time,
                price: param.price || this._series.coordinateToPrice(param.point?.y) || 0
            };
        }

        const { open, high, low, close } = seriesData;
        const mousePrice = param.price || this._series.coordinateToPrice(param.point?.y) || close;

        // Find closest OHLC value efficiently
        const ohlcValues = [open, high, low, close].filter(val => val != null);
        if (ohlcValues.length === 0) {
            return { time: param.time, price: mousePrice };
        }

        const closestPrice = ohlcValues.reduce((closest, current) =>
            Math.abs(mousePrice - current) < Math.abs(mousePrice - closest) ? current : closest
        );

        return { time: param.time, price: closestPrice };
    }
}

/*
Usage Examples:

// Create the drawing tool
const drawingTool = new RectangleDrawingTool(chart, series, {
    // Rectangle options
    fillColor: 'rgba(200, 50, 100, 0.75)',
    showCenterLine: true,
    centerLineWidth: 0.5,

    // Horizontal line options
    horizontalLine: {
        lineColor: 'rgba(41, 98, 255, 0.8)',
        lineWidth: 1,
        lineStyle: 0 // 0=solid, 1=dashed, 2=dotted
    }
});

// Switch to rectangle drawing mode (default)
drawingTool.setDrawingMode('rectangle');
drawingTool.startDrawing();

// Switch to horizontal line drawing mode
drawingTool.setDrawingMode('horizontalLine');
drawingTool.startDrawing();

// Get all horizontal lines
const horizontalLines = drawingTool.getHorizontalLines();

// Remove all horizontal lines
drawingTool.removeAllHorizontalLines();

// Selection:
// - Ctrl+Click to select rectangles or horizontal lines
// - Click outside to deselect
// - Selected shapes show toolbox with color, style, and delete options

// Features:
// 1. Reduced center line size (0.5px width, smaller dash pattern)
// 2. Fixed clicking outside rectangles now properly deselects and hides toolbox
// 3. New horizontal line tool with:
//    - Follows mouse movement and Ctrl modes
//    - Color picker support
//    - Line style toggle (solid/dashed/dotted)
//    - Selection and deletion
//    - Proper cleanup and state management
*/